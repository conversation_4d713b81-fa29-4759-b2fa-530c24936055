import { createStore, applyMiddleware } from 'redux'
import thunk from 'redux-thunk'
import { createLogger } from 'redux-logger'
import { rootReducer } from './reducer'
import { composeWithDevTools } from 'redux-devtools-extension'

const loggerMiddleware: any = createLogger()
export const store = createStore(
    rootReducer,
    composeWithDevTools(applyMiddleware(thunk, loggerMiddleware))
)

export type RootReducerType = ReturnType<typeof rootReducer>
