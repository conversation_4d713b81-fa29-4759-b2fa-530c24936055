import {
    I<PERSON><PERSON>,
    I<PERSON><PERSON>r,
    IListAssetHistoryResponse,
    IListAssetResponse,
    IAssetCareType,
    IUpdateAssetHistoryResponse,
    initError
} from '@types'
import {
    GET_LIST_ASSET_HISTORY_FAILURE,
    GET_LIST_ASSET_HISTORY_PENDING,
    GET_LIST_ASSET_HISTORY_SUCCESS,
    GET_LIST_ASSETS_FAILURE,
    GET_LIST_ASSETS_PENDING,
    GET_LIST_ASSETS_SUCCESS,
    GET_LIST_ASSET_STATUS_FAILURE,
    GET_LIST_ASSET_STATUS_PENDING,
    GET_LIST_ASSET_STATUS_SUCCESS,
    UPDATE_ASSET_HISTORY_FAILURE,
    UPDATE_ASSET_HISTORY_PENDING,
    UPDATE_ASSET_HISTORY_SUCCESS
} from '../actions/assetMWGActions'

export interface IAssetState {
    listAssets: {
        loading: boolean
        error: IError
        data: IListAssetResponse | null
    }
    listAssetHistory: {
        loading: boolean
        error: IError
        data: IListAssetHistoryResponse | null
    }
    listAssetStatus: {
        loading: boolean
        error: IError
        data: IAssetCareType[]
    }
    updateAssetHistory: {
        loading: boolean
        error: IError
        data: IUpdateAssetHistoryResponse | null
    }
}

const initialState: IAssetState = {
    listAssets: {
        loading: false,
        error: initError,
        data: null
    },
    listAssetHistory: {
        loading: false,
        error: initError,
        data: null
    },
    listAssetStatus: {
        loading: false,
        error: initError,
        data: []
    },
    updateAssetHistory: {
        loading: false,
        error: initError,
        data: null
    }
}

export const assetReducer = (
    state = initialState,
    action: any
): IAssetState => {
    switch (action.type) {
        case GET_LIST_ASSETS_PENDING:
            return {
                ...state,
                listAssets: {
                    ...state.listAssets,
                    loading: true,
                    error: initError
                }
            }
        case GET_LIST_ASSETS_SUCCESS:
            if (
                state.listAssets.data &&
                action.payload.data.length > 0 &&
                action.meta?.iDisplayStart > 0
            ) {
                const mergedAssets: IAsset[] = [
                    ...(state.listAssets.data.data || []),
                    ...action.payload.data
                ]

                return {
                    ...state,
                    listAssets: {
                        loading: false,
                        error: initError,
                        data: {
                            ...action.payload,
                            data: mergedAssets
                        }
                    }
                }
            }

            return {
                ...state,
                listAssets: {
                    loading: false,
                    error: initError,
                    data: action.payload
                }
            }
        case GET_LIST_ASSETS_FAILURE:
            return {
                ...state,
                listAssets: {
                    ...state.listAssets,
                    loading: false,
                    error: action.payload
                }
            }

        case GET_LIST_ASSET_HISTORY_PENDING:
            return {
                ...state,
                listAssetHistory: {
                    ...state.listAssetHistory,
                    loading: true,
                    error: initError
                }
            }
        case GET_LIST_ASSET_HISTORY_SUCCESS:
            if (
                state.listAssetHistory.data &&
                action.payload.data.length > 0 &&
                action.meta?.iDisplayStart > 0
            ) {
                const mergedHistory = [
                    ...(state.listAssetHistory.data.data || []),
                    ...action.payload.data
                ]

                return {
                    ...state,
                    listAssetHistory: {
                        loading: false,
                        error: initError,
                        data: {
                            ...action.payload,
                            data: mergedHistory
                        }
                    }
                }
            }

            return {
                ...state,
                listAssetHistory: {
                    loading: false,
                    error: initError,
                    data: action.payload
                }
            }
        case GET_LIST_ASSET_HISTORY_FAILURE:
            return {
                ...state,
                listAssetHistory: {
                    ...state.listAssetHistory,
                    loading: false,
                    error: action.payload
                }
            }
        case GET_LIST_ASSET_STATUS_PENDING:
            return {
                ...state,
                listAssetStatus: {
                    ...state.listAssetStatus,
                    loading: true,
                    error: initError
                }
            }
        case GET_LIST_ASSET_STATUS_SUCCESS:
            return {
                ...state,
                listAssetStatus: {
                    loading: false,
                    error: initError,
                    data: action.payload?.data || null
                }
            }
        case GET_LIST_ASSET_STATUS_FAILURE:
            return {
                ...state,
                listAssetStatus: {
                    ...state.listAssetStatus,
                    loading: false,
                    error: action.payload
                }
            }
        case UPDATE_ASSET_HISTORY_PENDING:
            return {
                ...state,
                updateAssetHistory: {
                    ...state.updateAssetHistory,
                    loading: true,
                    error: initError
                }
            }
        case UPDATE_ASSET_HISTORY_SUCCESS:
            return {
                ...state,
                updateAssetHistory: {
                    loading: false,
                    error: initError,
                    data: action.payload
                }
            }
        case UPDATE_ASSET_HISTORY_FAILURE:
            return {
                ...state,
                updateAssetHistory: {
                    ...state.updateAssetHistory,
                    loading: false,
                    error: action.payload
                }
            }
        default:
            return state
    }
}
