import {
    IListAssetHistoryRequest,
    IListAssetHistoryResponse,
    IListAssetRequest,
    IListAssetResponse,
    initError,
    IAssetStatusList,
    IUpdateAssetHistoryRequest,
    IUpdateAssetHistoryResponse
} from '@types'
import {
    getListAssetHistoryService,
    getListAssetsService,
    getListAssetLocationService,
    updateAssetHistoryService
} from '../services'
import { RootReducerType } from '../store'

export const GET_LIST_ASSETS_SUCCESS = 'GET_LIST_ASSETS_SUCCESS'
export const GET_LIST_ASSETS_FAILURE = 'GET_LIST_ASSETS_FAILURE'
export const GET_LIST_ASSETS_PENDING = 'GET_LIST_ASSETS_PENDING'

export const GET_LIST_ASSET_HISTORY_SUCCESS = 'GET_LIST_ASSET_HISTORY_SUCCESS'
export const GET_LIST_ASSET_HISTORY_FAILURE = 'GET_LIST_ASSET_HISTORY_FAILURE'
export const GET_LIST_ASSET_HISTORY_PENDING = 'GET_LIST_ASSET_HISTORY_PENDING'

export const GET_LIST_ASSET_STATUS_SUCCESS = 'GET_LIST_ASSET_STATUS_SUCCESS'
export const GET_LIST_ASSET_STATUS_FAILURE = 'GET_LIST_ASSET_STATUS_FAILURE'
export const GET_LIST_ASSET_STATUS_PENDING = 'GET_LIST_ASSET_STATUS_PENDING'

export const UPDATE_ASSET_HISTORY_SUCCESS = 'UPDATE_ASSET_HISTORY_SUCCESS'
export const UPDATE_ASSET_HISTORY_FAILURE = 'UPDATE_ASSET_HISTORY_FAILURE'
export const UPDATE_ASSET_HISTORY_PENDING = 'UPDATE_ASSET_HISTORY_PENDING'

export const getListAssets = (
    request: IListAssetRequest,
    isLoading: boolean = true
) => {
    return async (dispatch: (action: any) => void) => {
        if (isLoading) {
            dispatch({
                type: GET_LIST_ASSETS_PENDING
            })
        }

        try {
            const requestData = {
                pageRequest: {
                    search: request.search,
                    iDisplayStart: request.iDisplayStart,
                    iDisplayLength: request.iDisplayLength
                }
            }

            const response: IListAssetResponse = await getListAssetsService(
                requestData
            )

            dispatch({
                type: GET_LIST_ASSETS_SUCCESS,
                payload: response,
                meta: {
                    iDisplayStart: request.iDisplayStart,
                    iDisplayLength: request.iDisplayLength,
                    search: request.search
                }
            })

            return response
        } catch (error) {
            dispatch({
                type: GET_LIST_ASSETS_FAILURE,
                payload: error || initError
            })

            return null
        }
    }
}

export const getListAssetHistory = (
    request: IListAssetHistoryRequest,
    isLoading: boolean = true
) => {
    return async (dispatch: (action: any) => void) => {
        if (isLoading) {
            dispatch({
                type: GET_LIST_ASSET_HISTORY_PENDING
            })
        }

        try {
            const requestData = {
                assetCode: request.assetCode,
                pageRequest: {
                    assetCode: '',
                    iDisplayStart: request.iDisplayStart,
                    iDisplayLength: request.iDisplayLength
                }
            }

            const response: IListAssetHistoryResponse =
                await getListAssetHistoryService(requestData)

            dispatch({
                type: GET_LIST_ASSET_HISTORY_SUCCESS,
                payload: response,
                meta: {
                    iDisplayStart: request.iDisplayStart,
                    iDisplayLength: request.iDisplayLength,
                    assetCode: request.assetCode
                }
            })

            return response
        } catch (error) {
            dispatch({
                type: GET_LIST_ASSET_HISTORY_FAILURE,
                payload: error || initError
            })

            return null
        }
    }
}

export const getListAssetLocation = () => {
    return async (dispatch: (action: any) => void) => {
        dispatch({
            type: GET_LIST_ASSET_STATUS_PENDING
        })

        try {
            const response: IAssetStatusList[] =
                await getListAssetLocationService()

            dispatch({
                type: GET_LIST_ASSET_STATUS_SUCCESS,
                payload: response
            })

            return response
        } catch (error) {
            dispatch({
                type: GET_LIST_ASSET_STATUS_FAILURE,
                payload: error || initError
            })

            return null
        }
    }
}

export const updateAssetHistory = (request: IUpdateAssetHistoryRequest) => {
    return async (
        dispatch: (action: any) => void,
        getState: () => RootReducerType
    ) => {
        dispatch({
            type: UPDATE_ASSET_HISTORY_PENDING
        })

        const { Toast } = getState().commonReducer

        try {
            const response: IUpdateAssetHistoryResponse =
                await updateAssetHistoryService(request)

            dispatch({
                type: UPDATE_ASSET_HISTORY_SUCCESS,
                payload: response
            })

            Toast.show({
                type: 'success',
                text2: 'Cập nhật lịch sử thành công',
                position: 'bottom'
            })

            return response
        } catch (error) {
            dispatch({
                type: UPDATE_ASSET_HISTORY_FAILURE,
                payload: error || initError
            })

            Toast.show({
                type: 'error',
                text2: 'Cập nhật lịch sử thất bại',
                position: 'bottom'
            })

            return null
        }
    }
}
