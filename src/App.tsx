import {
  createNavigationContainerRef,
  NavigationContainer,
} from "@react-navigation/native";
import React from "react";
import { LogBox, View } from "react-native";
import {
  SafeAreaInsetsContext,
  SafeAreaProvider,
} from "react-native-safe-area-context";
import { enableScreens } from "react-native-screens";
import { Provider } from "react-redux";
import { store } from "./store/index";
import MainNavigator from "./navigator/mainNavigator";
import { withAllProps } from "./hoc";

enableScreens();
LogBox.ignoreLogs(['Each child in a list should have a unique "key" prop']);

export const navigationRef = createNavigationContainerRef();
const App = (props: any) => {
  return (
    <SafeAreaProvider>
      <Provider store={store}>
        <NavigationContainer ref={navigationRef}>
          <SafeAreaInsetsContext.Consumer>
            {(insets) => {
              return <AppContainer {...insets} {...props} />;
            }}
          </SafeAreaInsetsContext.Consumer>
        </NavigationContainer>
      </Provider>
    </SafeAreaProvider>
  );
};

const AppContainer = withAllProps((props: any) => {
  return (
    <View style={{ flex: 1, backgroundColor: "orange" }}>
      <MainNavigator {...props} />
    </View>
  );
});

export default App;
