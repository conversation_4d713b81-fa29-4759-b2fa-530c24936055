import React, { memo, useCallback, useEffect, useMemo, useState } from 'react'
import { View, Dimensions, StyleSheet } from 'react-native'
import { requestPermission, openSetting } from '@mwg-kits/core'
import {
    Mixins,
    Skeleton,
    MyWrapper,
    NotifyModal,
    Colors,
    MyText
} from '@react-native-xwork'
import {
    Camera,
    useCameraDevice,
    useCodeScanner
} from 'react-native-vision-camera'
import { useDispatch } from 'react-redux'
import { assetMWGActions } from '@actions'

const { translate } = (global as any).props.getTranslateConfig()

const { width: SCREEN_WIDTH } = Dimensions.get('window')

const FRAME_WIDTH = SCREEN_WIDTH * 0.8

type Props = {
    navigation: any
}

const ScanQR = (props: Props) => {
    const device = useCameraDevice('back')
    const [isCameraReady, setIsCameraReady] = useState(false)
    const [isScanned, setIsScanned] = useState(false)
    const [isNotificationVisible, setIsNotificationVisible] = useState(false)

    const dispatch: any = useDispatch()

    useEffect(() => {
        requestCameraPermission()
    }, [])

    useEffect(() => {
        const unsubscribe = props.navigation.addListener('focus', () => {
            setIsScanned(false)
            dispatch(
                assetMWGActions.getListAssets({
                    search: '',
                    iDisplayStart: 0,
                    iDisplayLength: 10
                })
            )
        })

        return () => unsubscribe()
    }, [props.navigation])

    const requestCameraPermission = useCallback(async () => {
        try {
            await requestPermission('camera')
            setTimeout(() => {
                setIsCameraReady(true)
            }, 500)
        } catch (e) {
            setIsNotificationVisible(true)
        }
    }, [])

    const codeScanner = useCodeScanner({
        codeTypes: ['qr', 'code-128', 'code-39'],
        onCodeScanned: (codes) => {
            if (codes.length > 0 && !isScanned) {
                handleScanQRCode(codes[0].value || '')
            }
        }
    })

    const handleScanQRCode = useCallback(
        async (data: string) => {
            const response = await dispatch(
                assetMWGActions.getListAssets({
                    search: data,
                    iDisplayStart: 0,
                    iDisplayLength: 1
                })
            )

            if (response.data.length > 0) {
                props.navigation.navigate('AssetDetail', {
                    asset: response.data[0]
                })
                setIsScanned(true)
            }
        },
        [isScanned, dispatch, props.navigation]
    )

    const renderCamera = useMemo(() => {
        if (!device)
            return (
                <View style={styles.emptyContainer}>
                    <MyText
                        text={'Camera không khả dụng'}
                        category="bold.body.1"
                        style={styles.title}
                    />
                </View>
            )

        if (!isCameraReady)
            return (
                <View style={styles.container}>
                    <View style={{ marginBottom: Mixins.scale(16) }} />
                    <View style={styles.cameraWrapper}>
                        <Skeleton style={styles.camera} />
                    </View>
                </View>
            )

        return (
            <View style={styles.container}>
                <MyText
                    text="Quét mã tài sản"
                    category="bold.body.1"
                    style={styles.title}
                />
                <View style={styles.cameraWrapper}>
                    <Camera
                        style={styles.camera}
                        device={device}
                        isActive={!isScanned}
                        codeScanner={codeScanner}
                        outputOrientation="device"
                        photo={false}
                        video={false}
                        enableZoomGesture={false}
                    />
                    <View style={styles.overlayContainer}>
                        <View style={styles.topOverlay} />
                        <View style={styles.middleContainer}>
                            <View style={styles.sideOverlay} />
                            <View style={styles.frame} />
                            <View style={styles.sideOverlay} />
                        </View>
                        <View style={styles.bottomOverlay} />
                    </View>
                </View>
            </View>
        )
    }, [device, isCameraReady, isScanned, codeScanner])

    return (
        <MyWrapper
            navigation={props.navigation}
            componentsLeftTitle={'Quét mã'}
            isErrorIcon={{ uri: 'ic_error' }}
            buttonRetry={translate('retry')}
            wrapperContainerBaseStyle={{ flex: 1 }}>
            {renderCamera}
            <NotifyModal
                typeNotify="confirm"
                typeIcon="warning"
                isVisible={isNotificationVisible}
                title={translate('notify')}
                content={translate('camera_permission')}
                titleConfirm={translate('setting')}
                onConfirm={() => {
                    openSetting()
                    setIsNotificationVisible(false)
                }}
                onCancel={() => setIsNotificationVisible(false)}
            />
        </MyWrapper>
    )
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingVertical: Mixins.scale(16),
        gap: Mixins.scale(16),
        alignItems: 'center'
    },
    title: {
        color: Colors.TEXT_PRIMARY
    },
    emptyContainer: {
        flex: 1,
        alignItems: 'center',
        marginTop: Mixins.scale(16)
    },
    cameraWrapper: {
        width: FRAME_WIDTH,
        aspectRatio: 1,
        borderRadius: Mixins.scale(12),
        overflow: 'hidden',
        alignItems: 'center',
        justifyContent: 'center'
    },
    camera: {
        width: '100%',
        height: '100%'
    },
    overlayContainer: {
        ...StyleSheet.absoluteFillObject,
        justifyContent: 'space-between'
    },
    topOverlay: {
        height: '10%',
        width: '100%',
        backgroundColor: 'rgba(0,0,0,0.6)'
    },
    bottomOverlay: {
        height: '10%',
        width: '100%',
        backgroundColor: 'rgba(0,0,0,0.6)'
    },
    middleContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        height: '80%'
    },
    sideOverlay: {
        flex: 1,
        height: '100%',
        backgroundColor: 'rgba(0,0,0,0.6)'
    },
    frame: {
        width: '80%',
        height: '100%',
        borderWidth: 2,
        borderColor: Colors.BORDER_PRIMARY,
        borderRadius: Mixins.scale(8)
    }
})

export default memo(ScanQR)
