import { View, StyleSheet, TouchableOpacity, FlatList } from 'react-native'
import React, { useState, useCallback, memo, useMemo } from 'react'
import {
    BaseButton,
    Colors,
    Mixins,
    MyText,
    TextArea
} from '@react-native-xwork'
import { IAsset, IAssetCareType } from '@types'
import { useDispatch } from 'react-redux'
import { assetMWGActions } from '@actions'

interface AddHistoryProps {
    onClose?: () => void
    assetDetail: IAsset
    listLocation: IAssetCareType[]
}

interface StatusButtonProps {
    title: string
    isSelected: boolean
    onPress: () => void
}

const StatusButton = memo(
    ({ title, isSelected, onPress }: StatusButtonProps) => (
        <TouchableOpacity
            activeOpacity={0.8}
            style={[
                styles.statusButton,
                isSelected && styles.statusButtonSelected
            ]}
            onPress={onPress}>
            <MyText
                text={title}
                category="regular.body.2"
                style={
                    isSelected
                        ? styles.statusButtonTextSelected
                        : styles.statusButtonText
                }
            />
        </TouchableOpacity>
    )
)

const AddHistory = ({
    onClose,
    assetDetail,
    listLocation
}: AddHistoryProps) => {
    const [selectedType, setSelectedType] = useState(0)
    const [description, setDescription] = useState('')
    const dispatch: any = useDispatch()

    const handleStatusSelect = useCallback((statusId: number) => {
        setSelectedType(statusId)
    }, [])

    const balancedList = useMemo(() => {
        if (!listLocation) return []

        // Clone mảng gốc
        const updatedList = [...listLocation]

        // Nếu số lượng là lẻ, thêm một item "giả"
        if (updatedList.length % 2 !== 0) {
            updatedList.push({
                id: -1,
                caretypeName: '',
                caretypeCode: '',
                createTime: '',
                updateTime: '',
                status: ''
            } as IAssetCareType)
        }

        return updatedList
    }, [listLocation])

    const handleSubmit = useCallback(() => {
        const data = {
            assetId: assetDetail.id,
            assetCareTypeId: selectedType,
            assetCareTime: new Date().getTime(),
            description
        }

        if (selectedType && description.trim()) {
            dispatch(assetMWGActions.updateAssetHistory(data)).then(() => {
                onClose?.()
                dispatch(
                    assetMWGActions.getListAssetHistory(
                        {
                            assetCode: assetDetail.assetCode,
                            iDisplayStart: 0,
                            iDisplayLength: 10
                        },
                        false
                    )
                )
            })
        }
    }, [selectedType, description, onClose])

    const isFormValid = selectedType && description.trim()

    return (
        <View style={styles.container}>
            <View style={styles.section}>
                <MyText
                    text="Trạng thái yêu cầu"
                    category="bold.sub.title.2"
                    style={styles.sectionTitle}
                />

                <View style={styles.statusGrid}>
                    <FlatList
                        numColumns={2}
                        data={balancedList}
                        contentContainerStyle={styles.statusRow}
                        columnWrapperStyle={styles.statusRow}
                        renderItem={({ item }) => {
                            if (item.id === -1) {
                                return (
                                    <View
                                        style={[
                                            styles.statusButton,
                                            {
                                                backgroundColor: 'transparent',
                                                borderColor: 'transparent'
                                            }
                                        ]}
                                    />
                                )
                            }

                            return (
                                <StatusButton
                                    key={item.id}
                                    title={item.caretypeName}
                                    isSelected={selectedType === item.id}
                                    onPress={() => handleStatusSelect(item.id)}
                                />
                            )
                        }}
                        keyExtractor={(item) => item.id.toString()}
                    />
                </View>
            </View>

            <View style={styles.section}>
                <View style={styles.labelContainer}>
                    <MyText
                        text="Mô tả"
                        category="bold.sub.title.2"
                        style={styles.label}
                    />
                    <MyText
                        text="*"
                        category="bold.sub.title.2"
                        style={styles.requiredText}
                    />
                </View>
                <TextArea
                    value={description}
                    onChangeText={setDescription}
                    placeholder="Nhập thêm mô tả nhé!"
                    height={Mixins.scale(120)}
                />
            </View>

            <BaseButton
                text="Tạo"
                disabled={!isFormValid}
                onPress={handleSubmit}
            />
        </View>
    )
}

const styles = StyleSheet.create({
    container: {
        gap: Mixins.scale(20)
    },
    section: {
        gap: Mixins.scale(12)
    },
    sectionTitle: {
        color: Colors.TEXT_PRIMARY
    },
    statusGrid: {
        gap: Mixins.scale(16)
    },
    statusRow: {
        gap: Mixins.scale(8)
    },
    statusButton: {
        flex: 1,
        alignItems: 'center',
        backgroundColor: Colors.BG_NEUTRALS_SECONDARY,
        borderRadius: Mixins.scale(12),
        paddingVertical: Mixins.scale(8),
        borderWidth: 1,
        borderColor: Colors.BORDER_PRIMARY
    },
    statusButtonSelected: {
        backgroundColor: Colors.BG_SOFT_BRAND,
        borderColor: Colors.BORDER_ACTIVE
    },
    statusButtonText: {
        color: Colors.TEXT_PRIMARY
    },
    statusButtonTextSelected: {
        color: Colors.TEXT_BRAND,
        fontWeight: 'bold'
    },
    labelContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: Mixins.scale(4)
    },
    label: {
        color: Colors.TEXT_PRIMARY
    },
    requiredText: {
        color: Colors.BADGE_SOFT_RED_TEXT
    }
})

export default AddHistory
