import { View } from 'react-native'
import React, { useCallback, useState, useEffect } from 'react'
import { Mixins, MyWrapper } from '@react-native-xwork'
import { useSelector, useDispatch } from 'react-redux'
import { RootReducerType } from '../../store'
import { ListHistory } from './components'
import { assetMWGActions } from '@actions'

const { translate } = (global as any).props.getTranslateConfig()

interface Props {
    navigation: any
    route?: {
        params?: {
            assetCode?: string
        }
    }
}

const ListAssetHistory = (props: Props) => {
    const dispatch: any = useDispatch()
    const [currentPage, setCurrentPage] = useState(0)
    const [isLoadingMore, setIsLoadingMore] = useState(false)
    const [pageSize] = useState(10)

    const assetCode = props.route?.params?.assetCode

    const { data, loading, error } = useSelector(
        (state: RootReducerType) => state.assetReducer.listAssetHistory
    )

    const fetchHistory = useCallback((startIndex: number = 0, isLoadMore: boolean = false) => {
        if (!assetCode) return

        if (isLoadMore) {
            setIsLoadingMore(true)
        }

        dispatch(
            assetMWGActions.getListAssetHistory(
                {
                    assetCode: assetCode,
                    iDisplayStart: startIndex,
                    iDisplayLength: pageSize
                },
                !isLoadMore
            )
        ).finally(() => {
            if (isLoadMore) {
                setIsLoadingMore(false)
            }
        })
    }, [dispatch, assetCode, pageSize])

    useEffect(() => {
        if (assetCode) {
            setCurrentPage(0)
            fetchHistory(0, false)
        }
    }, [assetCode])

    const handleLoadMore = useCallback(() => {
        const newPage = currentPage + 1
        const startIndex = newPage * pageSize

        setCurrentPage(newPage)
        fetchHistory(startIndex, true)
    }, [currentPage, pageSize, fetchHistory])

    const retryFetch = useCallback(() => {
        setCurrentPage(0)
        fetchHistory(0, false)
    }, [fetchHistory])

    return (
        <MyWrapper
            isSuccess={!loading && error.code === -1}
            isError={error.code !== -1}
            isLoading={loading}
            actionRetry={retryFetch}
            navigation={props.navigation}
            componentsLeftTitle={'Lịch sử cập nhật'}
            isErrorIcon={{ uri: 'ic_error' }}
            buttonRetry={translate('retry')}
            wrapperContainerBaseStyle={{ flex: 1 }}>
            <View style={{ flex: 1, paddingVertical: Mixins.scale(16) }}>
                <ListHistory
                    historyData={data?.data || []}
                    totalRecords={data?.recordsFiltered}
                    isLoading={isLoadingMore}
                    onLoadMore={handleLoadMore}
                />
            </View>
        </MyWrapper>
    )
}

export default ListAssetHistory
