import { StyleSheet, TouchableOpacity, View } from 'react-native'
import React, { memo, useState, useCallback, useMemo, useEffect } from 'react'
import {
    Colors,
    Mixins,
    MyText,
    MyWrapper,
    BaseButton,
    BaseBottomSheet
} from '@react-native-xwork'
import { IAsset, IAssetHistoryDetail } from '@types'
import { useDispatch, useSelector } from 'react-redux'
import { RootReducerType } from '../../store/reducer'
import { AddHistory, ListHistory } from './components'
import { assetMWGActions } from '@actions'

const { translate } = (global as any).props.getTranslateConfig()

interface Props {
    navigation: any
    route: {
        params: {
            asset: IAsset
        }
    }
}

interface AssetInfoProps {
    asset: IAsset
}

const AssetDetailScreen = (props: Props) => {
    const { asset } = props.route.params
    const dispatch: any = useDispatch()

    const [pageSize] = useState(10)
    const [isModalVisible, setIsModalVisible] = useState(false)

    const { data, loading, error } = useSelector(
        (state: RootReducerType) => state.assetReducer.listAssetHistory
    )

    const {
        data: listLocation,
        loading: loadingLocation,
        error: errorLocation
    } = useSelector(
        (state: RootReducerType) => state.assetReducer.listAssetStatus
    )

    const fetchListHistory = useCallback(() => {
        dispatch(
            assetMWGActions.getListAssetHistory(
                {
                    assetCode: asset.assetCode,
                    iDisplayStart: 0,
                    iDisplayLength: pageSize
                },
                true
            )
        )
    }, [dispatch, asset, pageSize])

    const fetchListLocation = useCallback(() => {
        dispatch(assetMWGActions.getListAssetLocation())
    }, [dispatch])

    useEffect(() => {
        fetchListHistory()
        fetchListLocation()
    }, [])

    const handleToggleHistory = useCallback(() => {
        props.navigation.navigate('ListAssetHistory', {
            assetCode: asset.assetCode
        })
    }, [props.navigation, asset.assetCode])

    const handleAddHistory = useCallback(() => {
        setIsModalVisible(true)
    }, [])

    const toggleModal = useCallback(() => {
        setIsModalVisible(!isModalVisible)
    }, [isModalVisible])

    return (
        <MyWrapper
            isSuccess={!loading && error.code === -1}
            isError={error.code !== -1}
            isLoading={loading}
            isErrorIcon={{ uri: 'ic_error' }}
            buttonRetry={translate('retry')}
            navigation={props.navigation}
            actionRetry={fetchListHistory}
            componentsLeftTitle={'Tài sản trụ sở MWG'}
            wrapperContainerBaseStyle={{ flex: 1 }}>
            <View style={styles.container}>
                <AssetInfo asset={asset} />
                <HistorySection
                    onToggle={handleToggleHistory}
                    historyData={data?.data || []}
                />
            </View>
            <View style={styles.buttonContainer}>
                <BaseButton text="Thêm lịch sử" onPress={handleAddHistory} />
            </View>
            <BaseBottomSheet
                errorCode={errorLocation.code}
                isLoading={loadingLocation}
                errorServiceReason={errorLocation.errorServiceReason}
                reload={fetchListLocation}
                isKeyboardAvoidingView
                isAutoHeight
                titleName="Thêm lịch sử"
                isModalVisible={isModalVisible}
                toggleModal={toggleModal}>
                <AddHistory
                    onClose={toggleModal}
                    assetDetail={asset}
                    listLocation={listLocation}
                />
            </BaseBottomSheet>
        </MyWrapper>
    )
}

const AssetInfo = memo(({ asset }: AssetInfoProps) => {
    const assetData = useMemo(
        () => [
            {
                label: 'Mã:',
                value: asset.assetCode,
                valueStyle: styles.codeValue
            },
            {
                label: 'Nhóm:',
                value: asset.assetGroup?.groupName || '',
                valueStyle: styles.normalValue
            },
            {
                label: 'Tên tài sản:',
                value: asset.assetName,
                valueStyle: styles.normalValue
            },
            {
                label: 'Ngày nhập:',
                value: asset.entryDate,
                valueStyle: styles.normalValue
            },
            {
                label: 'Vị trí:',
                value: asset.assetLocation?.locationName || '',
                valueStyle: styles.normalValue
            }
        ],
        [asset]
    )

    return (
        <View style={styles.section}>
            <MyText
                text="Thông tin tài sản"
                category="bold.sub.title.1"
                style={styles.sectionTitle}
            />
            {assetData.map((item, index) => (
                <View
                    key={index}
                    style={[
                        styles.infoRow,
                        index !== assetData?.length - 1 && {
                            borderBottomWidth: 1,
                            borderBottomColor: Colors.BORDER_PRIMARY
                        }
                    ]}>
                    <View style={styles.labelContainer}>
                        <MyText
                            text={item.label}
                            category="regular.caption.1"
                            style={styles.label}
                        />
                    </View>
                    <View style={styles.valueContainer}>
                        <MyText
                            text={item.value}
                            category="bold.caption.1"
                            style={item.valueStyle}
                        />
                    </View>
                </View>
            ))}
        </View>
    )
})

interface HistorySectionProps {
    onToggle: () => void
    historyData: IAssetHistoryDetail[]
}

const HistorySection = memo(
    ({ onToggle, historyData }: HistorySectionProps) => {
        return (
            <View>
                <View style={styles.historyHeader}>
                    <MyText
                        text="Lịch sử cập nhật"
                        category="bold.body.1"
                        style={styles.sectionTitle}
                    />
                    <TouchableOpacity onPress={onToggle}>
                        <MyText
                            text={'Xem tất cả'}
                            category="button.small"
                            style={styles.toggleText}
                        />
                    </TouchableOpacity>
                </View>

                <ListHistory historyData={historyData} isScroll={false} />
            </View>
        )
    }
)

const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingVertical: Mixins.scale(16),
        gap: Mixins.scale(16)
    },
    section: {
        backgroundColor: Colors.BG_NEUTRALS_SECONDARY,
        borderRadius: Mixins.scale(16),
        paddingVertical: Mixins.scale(8),
        paddingHorizontal: Mixins.scale(10),
        gap: Mixins.scale(10)
    },
    sectionTitle: {
        color: Colors.TEXT_PRIMARY
    },
    infoRow: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        paddingBottom: Mixins.scale(10)
    },
    labelContainer: {
        width: Mixins.scale(100),
        paddingRight: Mixins.scale(8)
    },
    valueContainer: {
        flex: 1,
        alignItems: 'flex-end'
    },
    label: {
        color: Colors.TEXT_SECONDARY
    },
    codeValue: {
        color: Colors.TEXT_BRAND,
        textAlign: 'right'
    },
    normalValue: {
        color: Colors.TEXT_PRIMARY,
        textAlign: 'right'
    },
    historyHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: Mixins.scale(16)
    },
    toggleText: {
        color: Colors.BUTTON_LINK_BRAND_TEXT
    },
    noHistoryText: {
        color: Colors.TEXT_SECONDARY
    },
    buttonContainer: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        paddingHorizontal: Mixins.scale(16),
        paddingVertical: Mixins.scale(8),
        backgroundColor: Colors.BG_NEUTRALS_PRIMARY
    }
})

export default memo(AssetDetailScreen)
