import {
    ActivityIndicator,
    FlatList,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native'
import React, { memo, useEffect, useState, useCallback } from 'react'
import {
    Colors,
    Mixins,
    MyText,
    MyWrapper,
    SearchInput
} from '@react-native-xwork'
import { useDispatch, useSelector } from 'react-redux'
import { Image } from '@mwg-kits/components'
import { IMAGES } from '@assets'
import { assetMWGActions } from '@actions'
import { IAsset } from '@types'

import { RootReducerType } from '../../store/reducer'

const { translate } = (global as any).props.getTranslateConfig()

interface Props {
    navigation: any
}

const AssetMWGScreen = (props: Props) => {
    const dispatch: any = useDispatch()

    const [textSearch, setTextSearch] = useState('')
    const [currentPage, setCurrentPage] = useState(0)
    const [isLoading, setIsLoading] = useState(false)
    const [pageSize] = useState(10)

    const { loading, data, error } = useSelector(
        (state: RootReducerType) => state.assetReducer.listAssets
    )

    const fetchAssets = useCallback(() => {
        dispatch(
            assetMWGActions.getListAssets({
                search: textSearch,
                iDisplayStart: 0,
                iDisplayLength: pageSize
            })
        )
    }, [dispatch, pageSize, textSearch])

    useEffect(() => {
        fetchAssets()
    }, [])

    const handleSearch = useCallback(
        (text: string) => {
            setTextSearch(text)
            setCurrentPage(0)

            setTimeout(() => {
                dispatch(
                    assetMWGActions.getListAssets(
                        {
                            search: text,
                            iDisplayStart: 0,
                            iDisplayLength: pageSize
                        },
                        false
                    )
                )
            }, 300)
        },
        [dispatch, pageSize]
    )

    const handleLoadMore = useCallback(() => {
        setIsLoading(true)
        setCurrentPage((prevPage) => {
            const newPage = prevPage + 1

            dispatch(
                assetMWGActions.getListAssets(
                    {
                        search: textSearch,
                        iDisplayStart: newPage * pageSize,
                        iDisplayLength: pageSize
                    },
                    false
                )
            ).finally(() => {
                setIsLoading(false)
            })

            return newPage
        })
    }, [textSearch, pageSize, dispatch])

    const renderLoadMore = useCallback(() => {
        const hasMoreData =
            data && data.data && data.recordsFiltered > data.data.length
        if (!hasMoreData) return null

        return (
            <TouchableOpacity
                style={styles.loadMoreButton}
                onPress={handleLoadMore}
                disabled={isLoading}>
                {isLoading ? (
                    <ActivityIndicator size="small" color={Colors.TEXT_BRAND} />
                ) : (
                    <MyText
                        text={'Xem thêm'}
                        category="button.medium"
                        style={styles.loadMoreButtonText}
                    />
                )}
            </TouchableOpacity>
        )
    }, [isLoading, handleLoadMore, data])

    const onPressDetail = useCallback(
        (asset: IAsset) => {
            props.navigation.navigate('AssetDetail', { asset })
        },
        [props.navigation]
    )

    const onPressScanQR = useCallback(() => {
        props.navigation.navigate('ScanQR')
    }, [props.navigation])

    return (
        <MyWrapper
            isSuccess={!loading && error.code === -1}
            isError={error.code !== -1}
            isLoading={loading}
            actionRetry={fetchAssets}
            navigation={props.navigation}
            componentsLeftTitle={'Tài sản trụ sở MWG'}
            isErrorIcon={{ uri: 'ic_error' }}
            buttonRetry={translate('retry')}
            wrapperContainerBaseStyle={{ flex: 1 }}>
            <View style={styles.container}>
                <SearchInputBar
                    value={textSearch}
                    onChangeText={handleSearch}
                    onClear={() => handleSearch('')}
                    onPressScanQR={onPressScanQR}
                />
                <AssetList
                    data={data?.data || []}
                    textSearch={textSearch}
                    renderAssetItem={AssetItemRenderer}
                    renderLoadMore={renderLoadMore}
                    onPressDetail={onPressDetail}
                />
            </View>
        </MyWrapper>
    )
}

interface AssetItemProps {
    item: IAsset
    onPressDetail: (item: IAsset) => void
}

const AssetItemRenderer = memo(({ item, onPressDetail }: AssetItemProps) => {
    const DATA = [
        {
            label: 'Mã:',
            value: item.assetCode,
            valueStyle: styles.assetCode
        },
        {
            label: 'Nhóm:',
            value: item.assetGroup?.groupName,
            valueStyle: styles.assetValue
        },
        {
            label: 'Tên tài sản:',
            value: item.assetName,
            valueStyle: styles.assetValue
        },
        {
            label: 'Ngày nhập:',
            value: item.entryDate,
            valueStyle: styles.assetValue
        },
        {
            label: 'Vị trí:',
            value: item.assetLocation?.locationName,
            valueStyle: styles.assetValue
        }
    ]

    return (
        <View style={styles.assetItem}>
            {DATA.map((dataItem, index) => (
                <View key={index} style={styles.assetRow}>
                    <MyText
                        text={dataItem.label}
                        category="regular.caption.1"
                        style={styles.assetLabel}
                    />
                    <MyText
                        text={dataItem.value}
                        category="bold.caption.1"
                        style={dataItem.valueStyle}
                    />
                </View>
            ))}

            <TouchableOpacity
                style={styles.detailButton}
                onPress={() => onPressDetail(item)}>
                <MyText
                    text="Xem chi tiết"
                    category="button.small"
                    style={styles.detailButtonText}
                />
            </TouchableOpacity>
        </View>
    )
})

interface AssetListProps {
    data: IAsset[]
    textSearch: string
    renderAssetItem: React.ComponentType<{
        item: IAsset
        onPressDetail: (item: IAsset) => void
    }>
    renderLoadMore: () => React.ReactElement | null
    onPressDetail: (item: IAsset) => void
}

const AssetList = memo((props: AssetListProps) => {
    const renderItem = useCallback(
        (info: { item: IAsset }) => {
            const ItemComponent = props.renderAssetItem
            return (
                <ItemComponent
                    item={info.item}
                    onPressDetail={(item: IAsset) => props.onPressDetail(item)}
                />
            )
        },
        [props.renderAssetItem]
    )

    return (
        <FlatList
            showsVerticalScrollIndicator={false}
            data={props.data}
            renderItem={renderItem}
            keyExtractor={(item) => item.id.toString()}
            contentContainerStyle={styles.listContainer}
            ListEmptyComponent={() => (
                <View style={styles.emptyContainer}>
                    <MyText
                        text={
                            props.textSearch
                                ? 'Không tìm thấy tài sản'
                                : 'Danh sách tài sản trống'
                        }
                        category="bold.body.1"
                        style={styles.emptyText}
                    />
                </View>
            )}
            ListFooterComponent={props.renderLoadMore}
        />
    )
})

interface SearchInputBarProps {
    value: string
    onChangeText: (text: string) => void
    onClear: () => void
    onPressScanQR: () => void
}

const SearchInputBar = memo((props: SearchInputBarProps) => {
    return (
        <SearchInput
            value={props.value}
            onChangeText={props.onChangeText}
            placeholder="Nhập hoặc quét mã"
            onClear={props.onClear}
            rightComponent={
                <TouchableOpacity
                    onPress={props.onPressScanQR}
                    style={styles.qrScanContainer}>
                    <Image
                        // @ts-ignore
                        isLocal
                        source={IMAGES.ic_qr_code}
                        style={styles.qrScanIcon}
                    />
                </TouchableOpacity>
            }
        />
    )
})

const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingVertical: Mixins.scale(16),
        gap: Mixins.scale(16)
    },
    qrScanContainer: {
        marginLeft: Mixins.scale(16),
        width: Mixins.scale(36),
        height: Mixins.scale(36)
    },
    qrScanIcon: {
        width: '100%',
        height: '100%'
    },
    listContainer: {
        paddingTop: Mixins.scale(8),
        paddingBottom: Mixins.scale(16),
        gap: Mixins.scale(16)
    },
    assetItem: {
        backgroundColor: Colors.BG_NEUTRALS_SECONDARY,
        borderRadius: Mixins.scale(16),
        paddingHorizontal: Mixins.scale(16),
        paddingVertical: Mixins.scale(14),
        gap: Mixins.scale(6)
    },
    assetRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    assetLabel: {
        color: Colors.TEXT_SECONDARY
    },
    assetValue: {
        color: Colors.TEXT_PRIMARY
    },
    assetCode: {
        color: Colors.TEXT_BRAND
    },
    detailButton: {
        alignSelf: 'flex-end',
        marginTop: Mixins.scale(6)
    },
    detailButtonText: {
        color: Colors.BUTTON_LINK_BRAND_TEXT
    },
    emptyContainer: {
        marginTop: Mixins.scale(16),
        justifyContent: 'center',
        alignItems: 'center'
    },
    emptyText: {
        color: Colors.TEXT_SECONDARY
    },
    loadMoreButton: {
        marginTop: Mixins.scale(16),
        alignSelf: 'center'
    },
    loadMoreButtonText: {
        color: Colors.TEXT_BRAND
    }
})

export default memo(AssetMWGScreen)
