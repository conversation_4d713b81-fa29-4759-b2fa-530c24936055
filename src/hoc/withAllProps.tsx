import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setAllProps } from '../store/action';
import { RootReducerType } from '../store';

const withAllProps = (WrappedComponent: any) => (props: any) => {
    const dispatch = useDispatch();
    const commonReducer = useSelector(
        (state: RootReducerType) => state.commonReducer
    );
    useEffect(() => {
        dispatch(setAllProps(props));
    }, [props]);

    if (!commonReducer.isDoneSetProps) return null;
    else return <WrappedComponent {...props} />;
};

export default withAllProps;
