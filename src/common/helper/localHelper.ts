import { Platform } from "react-native";

export const sleep = (ms: number) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const getPlatformDevice = () => {
  return Platform.OS;
};

export const formatSearchText = (value: string): string => {
  if (!value) return "";
  return removeVietnameseTones(value)?.toUpperCase();
};

export const toNormalizeText = (value: string): string => {
  if (!value) return "";
  return removeVietnameseTones(value).toLowerCase();
};

export function removeVietnameseTones(str: string): string {
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
  str = str.replace(/đ/g, "d");
  str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
  str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
  str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
  str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
  str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
  str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
  str = str.replace(/Đ/g, "D");
  // Some system encode vietnamese combining accent as individual utf-8 characters
  // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
  str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
  str = str.replace(/\u02C6|\u0306|\u031B/g, ""); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
  // Remove extra spaces
  // Bỏ các khoảng trắng liền nhau
  str = str.replace(/ + /g, " ");
  str = str.trim();
  // Remove punctuations
  // Bỏ dấu câu, kí tự đặc biệt
  str = str.replace(
    // eslint-disable-next-line no-useless-escape
    /!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g,
    " " //eslint-disable-line
  );
  return str;
}

export function isNumber(obj: any) {
  return (
    obj !== undefined &&
    obj !== null &&
    !Number.isNaN(obj) &&
    obj.constructor === Number
  );
}

export function IsValidateObject(object: any) {
  return object !== undefined && object !== null;
}

export const isEmptyObject = (v: any) => {
  return !!v && v.constructor === Object && Object.keys(v).length === 0;
};

export const formatMoney = (
  amount: any,
  decimalCount = 0,
  decimal = ".",
  thousands = ".",
  currencyStr = "₫"
) => {
  try {
    decimalCount = Math.abs(decimalCount);
    decimalCount = Number.isNaN(decimalCount) ? 2 : decimalCount;

    const negativeSign = amount < 0 ? "-" : "";

    const i: any = parseInt(
      (amount = Math.abs(Number(amount) || 0).toFixed(decimalCount))
    ).toString();
    const j = i.length > 3 ? i.length % 3 : 0;
    return (
      negativeSign +
      (j ? i.substr(0, j) + thousands : "") +
      i.substr(j).replace(/(\d{3})(?=\d)/g, `$1${thousands}`) +
      (decimalCount
        ? decimal +
          Math.abs(amount - i)
            .toFixed(decimalCount)
            .slice(2)
        : "") +
      currencyStr
    );
  } catch (e) {
    console.log(e);
  }
};

export const formatDisplayPoint = (point: number, currencyStr?: string) => {
  try {
    if (!IsValidateObject(point)) {
      return "";
    }
    if (!isNumber(point)) {
      point = Number(point);
    }
    return formatMoney(point, 0, ".", ".", currencyStr);
  } catch (error) {
    return "";
  }
};

export const formatPoints = (value: number): string => {
  return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
};

export function getCurrentDatePart(
  part: "year" | "month" | "date" | "hour" | "minutes" | "seconds"
): number | null {
  const now = new Date();

  switch (part) {
    case "year":
      return now.getFullYear();
    case "month":
      return now.getMonth() + 1; // Months are 0-based, so add 1
    case "date":
      return now.getDate();
    case "hour":
      return now.getHours();
    case "minutes":
      return now.getMinutes();
    case "seconds":
      return now.getSeconds();
    default:
      return null; // Return null for unsupported parts
  }
}
