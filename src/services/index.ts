import {
    IListAssetHistoryRequest,
    IListAssetHistoryResponse,
    IAssetStatusList,
    IUpdateAssetHistoryRequest,
    IUpdateAssetHistoryResponse
} from './types'
import { IListAssetRequest, IListAssetResponse } from '@types'
import { apiBase, METHOD } from '@sdk/api'
import {
    GET_LIST_ASSET_HISTORY,
    GET_LIST_ASSETS,
    GET_LIST_ASSET_STATUS,
    UPDATE_ASSET_HISTORY
} from '../api'

export const getListAssetsService = async (
    request: IListAssetRequest | { pageRequest: IListAssetRequest }
) => {
    return new Promise<IListAssetResponse>(async (resolve, reject) => {
        try {
            const response: any = await apiBase(
                GET_LIST_ASSETS,
                METHOD.POST,
                request
            )

            if (response?.object) resolve(response.object)
            else throw {}
        } catch (error) {
            reject(error)
        }
    })
}

export const getListAssetHistoryService = async (
    request:
        | IListAssetHistoryRequest
        | { pageRequest: IListAssetHistoryRequest }
) => {
    return new Promise<IListAssetHistoryResponse>(async (resolve, reject) => {
        try {
            const response: any = await apiBase(
                GET_LIST_ASSET_HISTORY,
                METHOD.POST,
                request
            )

            if (response?.object) resolve(response.object)
            else throw {}
        } catch (error) {
            reject(error)
        }
    })
}

export const getListAssetLocationService = async () => {
    return new Promise<IAssetStatusList[]>(async (resolve, reject) => {
        try {
            const response: any = await apiBase(
                GET_LIST_ASSET_STATUS,
                METHOD.POST,
                {}
            )

            if (response?.object) resolve(response.object)
            else throw {}
        } catch (error) {
            reject(error)
        }
    })
}

export const updateAssetHistoryService = async (
    request: IUpdateAssetHistoryRequest
) => {
    return new Promise<IUpdateAssetHistoryResponse>(async (resolve, reject) => {
        try {
            const response: any = await apiBase(
                UPDATE_ASSET_HISTORY,
                METHOD.POST,
                request
            )

            if (response?.object) resolve(response.object)
            else throw {}
        } catch (error) {
            reject(error)
        }
    })
}
