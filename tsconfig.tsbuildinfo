{"root": ["./babel.config.js", "./index.js", "./react-native.config.js", "./webpack-production.config.mjs", "./webpack.config.mjs", "./shared/dependencies.mjs", "./src/app.tsx", "./src/actions/index.ts", "./src/api/constants.ts", "./src/api/index.ts", "./src/assets/index.ts", "./src/assets/images/index.ts", "./src/common/index.ts", "./src/common/reactuse.ts", "./src/common/helper/cypher.ts", "./src/common/helper/formattext.ts", "./src/common/helper/index.ts", "./src/common/helper/localhelper.ts", "./src/hoc/index.tsx", "./src/hoc/withallprops.tsx", "./src/navigator/mainnavigator.tsx", "./src/screens/index.ts", "./src/screens/assetmwg/index.tsx", "./src/services/index.ts", "./src/services/types.ts", "./src/store/action.ts", "./src/store/index.ts", "./src/store/reducer.ts", "./src/store/rootreducer.ts"], "errors": true, "version": "5.8.2"}