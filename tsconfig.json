{"extends": "@tsconfig/react-native/tsconfig.json", "compilerOptions": {"jsx": "react", "paths": {"@actions": ["./src/actions"], "@screens": ["./src/screens"], "@common": ["./src/common"], "@assets": ["./src/assets"], "@constants": ["./src/constants"], "@styles": ["./src/styles"], "@components": ["./src/components"], "@utils": ["./src/utils"], "@services": ["./src/services"], "@types": ["./src/services/types"], "@react-native-xwork": ["./node_modules/react-native-xwork"]}}}